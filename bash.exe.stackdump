Stack trace:
Frame         Function      Args
0007FFFF7A00  00021005FEBA (000210285F48, 00021026AB6E, 000000000000, 0007FFFF6900) msys-2.0.dll+0x1FEBA
0007FFFF7A00  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF7CD8) msys-2.0.dll+0x67F9
0007FFFF7A00  000210046832 (000210285FF9, 0007FFFF78B8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF7A00  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFF7A00  0002100690B4 (0007FFFF7A10, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFF7CE0  00021006A49D (0007FFFF7A10, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFDA5200000 ntdll.dll
7FFDA40E0000 KERNEL32.DLL
7FFDA2920000 KERNELBASE.dll
7FFDA4FF0000 USER32.dll
7FFDA28F0000 win32u.dll
7FFDA3280000 GDI32.dll
7FFDA2D10000 gdi32full.dll
7FFDA27A0000 msvcp_win.dll
000210040000 msys-2.0.dll
7FFDA2E50000 ucrtbase.dll
7FFDA2FA0000 advapi32.dll
7FFDA3BF0000 msvcrt.dll
7FFDA3460000 sechost.dll
7FFDA4630000 RPCRT4.dll
7FFDA1A70000 CRYPTBASE.DLL
7FFDA2850000 bcryptPrimitives.dll
7FFDA3240000 IMM32.DLL
